# -*- coding: utf-8 -*-
"""preprocessor.py.ipynb

Automatically generated by <PERSON>b.

Original file is located at
    https://colab.research.google.com/drive/1Ot943kylHhWlUIg1RA1EfSbJHP_VE_9e
"""

import re
import pandas as pd

f = open('/content/WhatsApp Chat with ITSOLERA (DL Team Delta) Summers 2025.txt','r',encoding='utf-8')

data = f.read()

print(data)

pattern = '\d{1,2}/\d{1,2}/\d{2,4},\s\d{1,2}:\d{2}\s-\s'

# Split the data into messages based on the pattern
messages = re.split(pattern, data)[1:] # Remove the empty string at the beginning

messages

date = re.findall(pattern, data)

date

df = pd.DataFrame({'user_message': messages[1:], 'message_date': dates})
# convert message_date type
df['message_date'] = pd.to_datetime(df['message_date'], format='%d/%m/%Y, %I:%M\u202f%p - ')

users = []
messages = []
for message in df['user_message']:
    entry = re.split('([\w\W]+?):\s', message)
    if entry[1:]:  # user name
        users.append(entry[1])
        messages.append(" ".join(entry[2:]))
    else:
        users.append('group_notification')
        messages.append(entry[0])

df['only_date'] = df['message_date'].dt.date
df['year'] = df['message_date'].dt.year
df['month_num'] = df['message_date'].dt.month
df['month'] = df['message_date'].dt.month_name()
df['day'] = df['message_date'].dt.day
df['day_name'] = df['message_date'].dt.day_name()
df['hour'] = df['message_date'].dt.hour
df['minute'] = df['message_date'].dt.minute

def add_period_column(df):
    period = []
    for hour in df[['day_name', 'hour']]['hour']:
        if hour == 23:
            period.append(str(hour) + "-" + str('00'))
        elif hour == 0:
            period.append(str('00') + "-" + str(hour + 1))
        else:
            period.append(str(hour) + "-" + str(hour + 1))

    df['period'] = period
    return df

